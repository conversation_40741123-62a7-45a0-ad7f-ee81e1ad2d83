
name: "cal"
description: A new Flutter project based on Clean Architecture.
version: 1.0.0+40

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8

  # Logic
  dartz: ^0.10.1
  equatable: ^2.0.5
  
  device_info_plus: ^11.4.0
  uuid: ^4.5.1
  

  # State Managment
  flutter_bloc: ^8.1.3
  bloc: ^8.1.4


  # DI 
  injectable: ^2.5.0
  get_it: ^8.0.3

  # Networking & API
  dio: ^5.3.0
  pretty_dio_logger: ^1.4.0

  # Storage
  shared_preferences: ^2.2.0

  # UI
  google_fonts: ^6.2.1
  text_scroll: ^0.2.0
  toastification: ^3.0.2
  cached_network_image: ^3.4.1
  flutter_svg: ^2.0.17
  easy_localization: ^3.0.7+1
  flutter_staggered_animations: ^1.1.1
  fl_chart: ^1.0.0
  wheel_slider: ^1.2.2
  in_app_purchase: ^3.2.3
  rename: ^3.1.0
  throttling: ^2.0.1
  animated_bottom_navigation_bar: ^1.4.0
  dotted_border: ^3.0.1
  syncfusion_flutter_charts: ^29.2.5
  camera: ^0.11.1
  permission_handler: ^12.0.0+1
  image_picker: ^1.1.2
  flutter_slidable: ^4.0.0
  shimmer: ^3.0.0
  timelines_plus: ^1.0.7
  # injectable_generator: ^2.7.0


  # Authentication packages
  sign_in_with_apple: ^6.1.2
  google_sign_in: ^6.2.1
  
  # Secure storage
  flutter_secure_storage: ^9.2.2

  
  # Firebase
  firebase_core: ^3.14.0
  # firebase_messaging: ^15.2.6
  # flutter_local_notifications: ^19.2.1
  firebase_auth: ^5.6.0
  
  #health_tracker
  # health: ^13.1.0


  # Database
  isar: ^3.1.0+1
  isar_flutter_libs: 3.1.0+1
  path_provider: ^2.1.5
  path: ^1.9.1
  catcher_2: ^2.1.3
  tab_indicator_styler: ^2.0.0
  http_parser: ^4.1.2
  animated_flip_counter: ^0.3.4
  animated_digit: ^3.2.3
  firebase_messaging: ^15.2.7
  flutter_local_notifications: ^19.3.0
  haptic_feedback: ^0.5.1+1
  logger: ^2.6.0
  connectivity_plus: ^6.1.4
  url_launcher: ^6.3.1
  webview_flutter: ^4.13.0
  flutter_launcher_icons: ^0.14.4
  google_mlkit_barcode_scanning: ^0.14.1

  # window_manager: ^0.4.3


dev_dependencies:
  flutter_lints: ^2.0.0 
  build_runner: ^2.4.13
  isar_generator: ^3.1.0+1


flutter_launcher_icons:
  image_path: "assets/images/app_icon.png"
  android: true
  ios: true
  remove_alpha_ios: true



flutter:
  uses-material-design: true

  assets:
    - assets/translations/
    - assets/images/onboarding/
    - assets/images/
    - assets/images/gif/
    - assets/images/settings/
    - assets/images/quick_actions/
    - assets/images/exercises/


flutter_assets:
  assets_path: assets/
  output_path: lib/generated/
  filename: assets.dart
  field_rename: camelCase
